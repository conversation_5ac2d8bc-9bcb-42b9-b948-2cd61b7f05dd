-- Drop existing versions of the function
DROP FUNCTION IF EXISTS public.tms_ace_get_capacity_by_resource(text, date, date);
DROP FUNCTION IF EXISTS public.tms_ace_get_capacity_by_resource(text, text, text);

-- Create function to get capacity data from cl_tx_capacity table
CREATE OR REPLACE FUNCTION public.tms_ace_get_capacity_by_resource(
    p_resource_id text,
    p_start_date text DEFAULT NULL,
    p_end_date text DEFAULT NULL
)
RETURNS json
LANGUAGE plpgsql AS $function$
DECLARE
    status boolean;
    message text;
    resp_data json;
    capacity_records json;
    start_date_parsed date;
    end_date_parsed date;
BEGIN
    -- Initialize default values
    status = false;
    message = 'Internal_error';
    
    -- Validate input parameters
    IF p_resource_id IS NULL OR p_resource_id = '' THEN
        message = 'resource_id_required';
        resp_data = json_build_object(
            'status', 'error',
            'message', 'Resource ID is required',
            'timestamp', now()
        );
        RETURN json_build_object('status', status, 'code', message, 'data', resp_data);
    END IF;
    
    -- Parse and validate dates
    BEGIN
        IF p_start_date IS NOT NULL AND p_start_date != '' THEN
            start_date_parsed = p_start_date::date;
        ELSE
            -- Default to tomorrow if not provided
            start_date_parsed = (CURRENT_DATE + INTERVAL '1 day')::date;
        END IF;
        
        IF p_end_date IS NOT NULL AND p_end_date != '' THEN
            end_date_parsed = p_end_date::date;
        ELSE
            -- Default to day after start_date if not provided
            end_date_parsed = (start_date_parsed + INTERVAL '1 day')::date;
        END IF;
    EXCEPTION WHEN OTHERS THEN
        message = 'invalid_date_format';
        resp_data = json_build_object(
            'status', 'error',
            'message', 'Invalid date format. Use YYYY-MM-DD format',
            'timestamp', now()
        );
        RETURN json_build_object('status', status, 'code', message, 'data', resp_data);
    END;
    
    -- Validate date range
    IF end_date_parsed < start_date_parsed THEN
        message = 'invalid_date_range';
        resp_data = json_build_object(
            'status', 'error',
            'message', 'End date cannot be before start date',
            'timestamp', now()
        );
        RETURN json_build_object('status', status, 'code', message, 'data', resp_data);
    END IF;
    
    -- Query capacity data from cl_tx_capacity table
    SELECT json_agg(
        json_build_object(
            'resourceId', cap.resource_id,
            'resourceLabel', cap.resource_label,
            'startTime', cap.start_time,
            'endTime', cap.end_time,
            'totalCapacity', cap.total_capacity,
            'availableCapacity', cap.available_capacity,
            'bookedCapacity', cap.booked_capacity,
            'date', cap.usr_tmzone_day
        ) ORDER BY cap.start_time ASC
    )
    INTO capacity_records
    FROM public.cl_tx_capacity cap
    WHERE cap.resource_id = p_resource_id
    AND cap.usr_tmzone_day >= start_date_parsed
    AND cap.usr_tmzone_day <= end_date_parsed;
    
    -- Handle case when no records found
    IF capacity_records IS NULL THEN
        capacity_records = '[]'::json;
    END IF;
    
    -- Build successful response
    status = true;
    message = 'success';
    resp_data = json_build_object(
        'status', 'success',
        'message', 'Capacity data retrieved successfully',
        'data', capacity_records,
        'timestamp', now(),
        'source', 'database',
        'query_params', json_build_object(
            'resource_id', p_resource_id,
            'start_date', start_date_parsed,
            'end_date', end_date_parsed
        )
    );
    
    -- Return in the standard format
    RETURN json_build_object('status', status, 'code', message, 'data', resp_data);
    
EXCEPTION WHEN OTHERS THEN
    -- Handle any unexpected errors
    message = 'database_error';
    resp_data = json_build_object(
        'status', 'error',
        'message', 'Database error: ' || SQLERRM,
        'timestamp', now()
    );
    RETURN json_build_object('status', status, 'code', message, 'data', resp_data);
END;
$function$;
